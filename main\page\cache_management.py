#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: cache_management.py
@time: 2025/7/23 
@Description: 缓存管理页面
"""
import asyncio

import streamlit as st
import pandas as pd
import time
from typing import NoReturn

from common.cache_monitor import cache_manager
from common.cache_manager import global_cache
from common.myLog import logger


def render_cache_dashboard():
    """渲染缓存仪表板"""
    st.header("🗄️ 缓存管理仪表板")
    
    # 获取仪表板数据
    dashboard_data = cache_manager.get_dashboard_data()
    current_metrics = dashboard_data["current_metrics"]
    
    # 当前状态概览
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "缓存大小", 
            f"{current_metrics['cache_size']}/{current_metrics['max_size']}",
            delta=None
        )
    
    with col2:
        hit_rate = current_metrics['hit_rate']
        st.metric(
            "命中率", 
            f"{hit_rate:.2%}",
            delta=None,
            delta_color="normal" if hit_rate > 0.7 else "inverse"
        )
    
    with col3:
        total_requests = current_metrics['hits'] + current_metrics['misses']
        st.metric("总请求数", total_requests)
    
    with col4:
        st.metric("驱逐次数", current_metrics['evictions'])


def render_cache_details():
    """渲染缓存详细信息"""
    st.subheader("🔍 缓存详情")
    
    cache_info = global_cache.get_cache_info()
    
    if cache_info:
        # 转换为DataFrame以便显示
        cache_data = []
        for key, info in cache_info.items():
            cache_data.append({
                "缓存键": key,
                "创建时间": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(info['created_at'])),
                "最后访问": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(info['last_accessed'])),
                "访问次数": info['access_count'],
                "TTL(秒)": info['ttl'] if info['ttl'] else "永不过期",
                "是否过期": "是" if info['is_expired'] else "否"
            })
        
        df = pd.DataFrame(cache_data)
        st.dataframe(df, use_container_width=True)
    else:
        st.info("当前没有缓存数据")


async def clear_cache() -> None:
    """清理系统缓存"""
    try:
        st.session_state.clear()
        st.cache_data.clear()
        st.cache_resource.clear()
        st.toast("清理缓存成功, 稍后将自动退出登录!")
        await asyncio.sleep(1)
        st.session_state.menu = ""
        st.rerun()
    except Exception as e:
        logger.error(f"清理缓存失败: {str(e)}")
        st.error("清理缓存失败")

async def render_cache_operations():
    """渲染缓存操作"""
    st.subheader("🛠️ 缓存操作")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🧹 清理过期缓存", type="secondary"):
            with st.spinner("正在清理过期缓存..."):
                initial_stats = global_cache.get_stats()
                global_cache._cleanup_expired()
                final_stats = global_cache.get_stats()
                
                cleared = initial_stats["cache_size"] - final_stats["cache_size"]
                if cleared > 0:
                    st.success(f"✅ 清理了 {cleared} 个过期缓存项")
                else:
                    st.info("ℹ️ 没有发现过期的缓存项")
    
    with col2:
        if st.button("🗑️ 清空所有自定义缓存", type="secondary"):
            if st.session_state.get("confirm_clear_all", False):
                global_cache.clear()
                st.success("✅ 所有缓存已清空")
                st.session_state["confirm_clear_all"] = False
            else:
                st.session_state["confirm_clear_all"] = True
                st.warning("⚠️ 再次点击确认清空所有缓存")
    
    with col3:
        if st.button("🔄 优化缓存", type="secondary"):
            with st.spinner("正在优化缓存..."):
                result = cache_manager.optimize_cache()
                
                if result["performed"]:
                    st.success("✅ 优化完成:")
                    for action in result["performed"]:
                        st.write(f"- {action}")
                else:
                    st.info("ℹ️ 缓存已经是最优状态")

    with col4:
        if st.button("🔄 清空系统缓存", type="secondary"):
            with st.spinner("正在清空系统缓存..."):
                if  st.session_state:
                    await clear_cache()
                else:
                    st.info("ℹ️ 缓存已经是最优状态")
    
    # 按模式清理
    st.write("---")
    st.write("**按模式清理缓存**")
    
    col1, col2 = st.columns([3, 1])
    with col1:
        pattern = st.text_input(
            "输入要清理的缓存键模式", 
            placeholder="例如: router_, rag_CSQ_",
            help="输入缓存键的部分内容，将清理包含该模式的所有缓存项"
        )
    
    with col2:
        if st.button("清理", type="primary", disabled=not pattern):
            if pattern:
                cleared_count = cache_manager.clear_cache_by_pattern(pattern)
                if cleared_count > 0:
                    st.success(f"✅ 清理了 {cleared_count} 个匹配的缓存项")
                else:
                    st.info("ℹ️ 没有找到匹配的缓存项")


async def init_cache_management() -> NoReturn:
    """缓存管理页面初始化"""
    try:
        # 渲染各个部分
        render_cache_dashboard()
        st.write("---")
        render_cache_details()
        st.write("---")
        render_cache_operations()
        
    except Exception as e:
        logger.error(f"缓存管理页面错误: {str(e)}", exc_info=True)
        st.error(f"页面加载失败: {str(e)}")
