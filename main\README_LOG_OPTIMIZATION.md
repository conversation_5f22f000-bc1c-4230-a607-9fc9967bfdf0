# 日志系统优化指南

## 概述

本项目已完成日志系统的全面优化，提供了统一、高效、易用的日志记录解决方案。优化包括：

- 🔧 **统一的日志工具类** - 提供一致的日志记录接口
- 🎯 **智能装饰器** - 自动记录性能、业务事件和数据库操作
- 📊 **分类日志文件** - 按类型分离日志，便于分析和监控
- 🛠️ **自动化工具** - 审计和优化现有代码中的日志使用
- 📖 **最佳实践文档** - 详细的使用指南和规范

## 主要改进

### 1. 日志工具类 (`common/log_optimizer.py`)

提供了统一的日志记录接口：

```python
from common.log_optimizer import log_info, log_error, log_warning, log_debug

# 结构化日志记录
log_info("用户登录成功", user_id="12345", user_name="张三")
log_error("操作失败", error=exception, context="用户注册")
```

### 2. 智能装饰器

#### 性能监控装饰器
```python
@performance_logger(threshold=2.0)
def slow_operation():
    # 自动记录执行时间，超过阈值时发出警告
    pass
```

#### 业务事件装饰器
```python
@business_logger("用户注册")
def register_user(user_data):
    # 自动记录业务事件的开始和完成
    pass
```

#### 数据库操作装饰器
```python
@database_logger("tb_user")
def add_user(user_info):
    # 自动记录数据库操作的性能和结果
    pass
```

### 3. 增强的日志配置

- **应用日志** (`logs/app.log`) - 一般应用程序日志
- **错误日志** (`logs/error.log`) - 错误和异常信息
- **性能日志** (`logs/performance.log`) - 性能监控数据
- **业务日志** (`logs/business.log`) - 业务事件记录

### 4. 已优化的文件

以下文件已完成日志优化：

- ✅ `common/initialize.py` - 系统初始化日志优化
- ✅ `common/utils.py` - 工具函数错误处理优化
- ✅ `core/tools.py` - 搜索工具性能监控
- ✅ `page/document.py` - 文档处理业务日志
- ✅ `model/curd.py` - 数据库操作日志
- ✅ `common/myLog.py` - 日志配置增强

## 使用指南

### 快速开始

1. **导入日志工具**：
```python
from common.log_optimizer import log_info, log_error, performance_logger
```

2. **记录日志**：
```python
# 信息日志
log_info("操作完成", user_id=123, operation="update")

# 错误日志
try:
    risky_operation()
except Exception as e:
    log_error("操作失败", error=e, user_id=123)
```

3. **使用装饰器**：
```python
@performance_logger(threshold=1.0)
def important_function():
    # 函数逻辑
    pass
```

### 迁移现有代码

对于现有代码，可以使用自动化工具进行优化：

#### 1. 审计现有日志使用
```bash
cd main
python scripts/log_audit.py --project-root . --output audit_report.md
```

#### 2. 自动优化日志代码
```bash
cd main
python scripts/log_optimizer_script.py --project-root . --output optimization_summary.md
```

**注意**: 自动优化会创建备份，但建议在运行前提交代码到版本控制系统。

### 手动优化步骤

1. **替换 print 语句**：
```python
# 旧方式
print(f"用户 {user_id} 登录成功")

# 新方式
log_info("用户登录成功", user_id=user_id)
```

2. **优化异常处理**：
```python
# 旧方式
try:
    operation()
except Exception as e:
    logger.error(f"操作失败: {e}")

# 新方式
try:
    operation()
except Exception as e:
    log_error("操作失败", error=e, context="具体操作描述")
```

3. **添加性能监控**：
```python
# 为关键函数添加性能监控
@performance_logger(threshold=2.0)
def critical_business_logic():
    pass
```

## 工具使用

### 日志审计工具

检查代码中的日志使用问题：

```bash
# 审计整个项目
python scripts/log_audit.py

# 审计特定目录
python scripts/log_audit.py --directory page/

# 指定输出文件
python scripts/log_audit.py --output my_audit_report.md
```

### 自动优化工具

自动优化代码中的日志使用：

```bash
# 优化整个项目（会创建备份）
python scripts/log_optimizer_script.py

# 优化特定目录
python scripts/log_optimizer_script.py --directory core/

# 不创建备份（谨慎使用）
python scripts/log_optimizer_script.py --no-backup
```

## 最佳实践

### 1. 日志级别使用

- **DEBUG**: 详细调试信息，仅开发环境
- **INFO**: 正常业务流程记录
- **WARNING**: 需要注意但不影响运行的问题
- **ERROR**: 错误信息，程序可继续运行
- **CRITICAL**: 严重错误，程序无法继续

### 2. 结构化日志

使用关键字参数而不是字符串拼接：

```python
# ✅ 推荐
log_info("用户操作", user_id=123, action="login", ip="***********")

# ❌ 避免
log_info(f"用户 {user_id} 从 {ip} 执行了 {action} 操作")
```

### 3. 性能监控

为关键业务函数添加性能监控：

```python
@performance_logger(threshold=1.0)  # 超过1秒记录为慢操作
def important_business_function():
    pass
```

### 4. 安全考虑

- 不记录敏感信息（密码、令牌等）
- 对用户输入进行适当清理
- 限制日志文件大小和数量

## 监控和维护

### 日志文件管理

- 自动轮转：默认10MB一个文件
- 备份保留：最近30个文件
- 定期清理：建议设置定时任务清理过期日志

### 推荐的监控工具

1. **ELK Stack** (Elasticsearch, Logstash, Kibana)
2. **Grafana** + **Loki**
3. **Fluentd** + **Prometheus**

### 告警设置

建议为以下情况设置告警：

- 错误日志数量异常增加
- 慢操作频繁出现
- 磁盘空间不足
- 日志文件无法写入

## 配置说明

### 环境变量

- `LOG_LEVEL`: 设置日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `LOG_PATH`: 设置日志文件路径

### 配置文件

在 `config.yaml` 中可以调整日志配置：

```yaml
logging:
  level: "INFO"
  handlers:
    file:
      max_bytes: 10485760  # 10MB
      backup_count: 30     # 保留30个备份
```

## 常见问题

### Q: 如何在不同环境使用不同日志级别？

A: 通过环境变量 `LOG_LEVEL` 设置，或在配置文件中为不同环境设置不同值。

### Q: 自动优化工具安全吗？

A: 工具会自动创建备份，但建议在使用前提交代码到版本控制系统。

### Q: 如何禁用某些日志？

A: 调整日志级别或在配置中禁用特定处理器。

### Q: 性能监控会影响系统性能吗？

A: 影响很小，装饰器只记录时间戳，不会显著影响性能。

## 技术支持

如有问题或建议，请：

1. 查看 `docs/logging_best_practices.md` 详细文档
2. 运行审计工具检查代码规范
3. 联系开发团队获取支持

---

**注意**: 本优化方案已在现有代码基础上实施，确保向后兼容的同时提供了更好的日志记录体验。
