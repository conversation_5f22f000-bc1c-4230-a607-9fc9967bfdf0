#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: initialize.py 
@time: 2024/9/29 15:07
@Description: 
"""
import json
import streamlit as st

from common.dingTalk import DingTalkAPI, DingTalkAPIError
from common.myLog import logger
from common.log_optimizer import performance_logger, business_logger, log_info, log_error, log_warning
from model.curd import CRUD

CACHE_TIME: int = 3600
logger.name = __name__


def init_data():

    st.query_params.clear()

    if "llmType" not in st.session_state:
        st.session_state["llmType"] = "ZhiPuAi-glm4"
        st.session_state["llmTypeIndex"] = 1

    if "searchType" not in st.session_state:
        st.session_state["searchType"] = "Rerank"
        st.session_state["searchTypeIndex"] = 3

    if 'uploaded_files' not in st.session_state:
        st.session_state.uploaded_files = []

    if "chunk_size" not in st.session_state:
        st.session_state.chunk_size = 500

    if "chunk_overlap" not in st.session_state:
        st.session_state.chunk_overlap = 200

    if "max_token" not in st.session_state:
        st.session_state.max_token = 0

    if "temperature" not in st.session_state:
        st.session_state.temperature = 0.1

    if "filePath" not in st.session_state:
        st.session_state.filePath = None

    if "collection_name" not in st.session_state:
        st.session_state.collection_name = None

    if "FN" not in st.session_state:
        st.session_state.FN = None

    if "FS" not in st.session_state:
        st.session_state.FS = None

    log_info("系统初始化完成", components=["session_state", "config", "cache"])

@st.cache_data(ttl=CACHE_TIME)
@business_logger("用户登录")
def Login(qr_code: dict) -> None:
    if isinstance(qr_code, dict):
        try:
            log_info("开始钉钉用户认证", auth_code=qr_code.get("authCode", "")[:10] + "...")
            dingTalk = DingTalkAPI(qr_code["authCode"])
            userInfo = dingTalk.get_user_info()
            if userInfo:
                st.session_state.curd.add_user(**userInfo)
                st.session_state.userInfo = st.session_state.curd.get_user_info(**userInfo)
                log_info("用户登录成功", user_id=userInfo.get("dt_userid"), user_name=userInfo.get("dt_name"))
            else:
                log_warning("钉钉认证成功但未获取到用户信息")
        except Exception as e:
            log_error("用户登录失败", error=e, auth_code=qr_code.get("authCode", "")[:10] + "...")


@st.cache_resource(ttl=CACHE_TIME)
@performance_logger(threshold=2.0)
def init_curd():
    log_info("初始化CRUD实例")
    return CRUD()


@performance_logger(threshold=1.0)
@business_logger("LLM配置初始化")
def init_llm():
    try:
        log_info("开始加载LLM配置")
        configs = st.session_state.curd.getConfigValues(["llm", "llm_comm", "emb", "search_type", "base_k", "top_k"])

        st.session_state.llm = json.loads(configs["llm"])
        st.session_state.llm_comm = json.loads(configs["llm_comm"])
        st.session_state.emb = json.loads(configs["emb"])
        st.session_state.search_type = configs["search_type"]
        st.session_state.base_k = configs["base_k"]
        st.session_state.top_k = configs["top_k"]

        log_info(
            "LLM配置加载完成",
            search_type=configs["search_type"],
            base_k=configs["base_k"],
            top_k=configs["top_k"]
        )

    except Exception as e:
        log_error("LLM配置初始化失败", error=e)