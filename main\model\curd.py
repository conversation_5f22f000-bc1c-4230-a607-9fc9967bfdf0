#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: curd.py 
@time: 2024/11/5 14:21
@Description: 
"""

from sqlalchemy import func, and_

from model import Session
from model.models import <PERSON>b<PERSON><PERSON>, TbMessage, TbRole, TbAuth, TbConfig, TbKnowledge
from common.utils import start_date
from common.log_optimizer import database_logger, log_info, log_warning, log_error


class CRUD:
    def __init__(self):
        self.session = Session()

    def _add(self,
             model,
             **kwargs
             ) -> object:
        try:
            instance = model(**kwargs)
            self.session.add(instance)
            self.session.commit()
            return instance
        except Exception as e:
            self.session.rollback()
            raise e
        finally:
            self.session.close()

    @staticmethod
    def _build_query_conditions(model, **kwargs):
        conditions = []
        for key, value in kwargs.items():
            if hasattr(model, key):
                conditions.append(getattr(model, key) == value)
        return conditions

    def _exist(
            self,
            model,
            **kwargs
    ) -> bool:
        try:
            conditions = self._build_query_conditions(
                model,
                **kwargs
            )
            query = self.session.query(model).filter(
                *conditions
            )
            ant = self.session.query(query.exists()).scalar()
            return ant
        except Exception as e:
            self.session.rollback()
            raise e
        finally:
            self.session.close()

    def update_user(
            self,
            model: TbUser = TbUser,
            **kwargs
    ) -> None:
        try:
            query = self.session.query(model).filter(
                model.dt_userid == kwargs['dt_userid']
            )
            query.update(kwargs)
            self.session.commit()
        except Exception as e:
            self.session.rollback()
            raise e

    def update_knowledge(
            self,
            model: TbKnowledge,
            **kwargs
    ) -> None:
        try:
            query = self.session.query(model).filter(
                model.document_name == kwargs['document_name']
            )
            query.update(kwargs)
            self.session.commit()
        except Exception as e:
            self.session.rollback()
            raise e

    def update_role(
            self,
            model: TbRole = TbRole,
            **kwargs
    ) -> None:
        try:
            query = self.session.query(model).filter(
                model.id == kwargs['id']
            )
            query.update(kwargs)
            self.session.commit()
        except Exception as e:
            self.session.rollback()
            raise e

    def update_config(
            self,
            model: TbConfig = TbConfig,
            **kwargs
    ) -> None:
        try:
            query = self.session.query(model).filter(
                model.id == kwargs['id']
            )
            query.update(kwargs)
            self.session.commit()
        except Exception as e:
            self.session.rollback()
            raise e

    def _all(
            self,
            model,
    ) -> tuple:
        query = (
            self.session.query(model)
            .order_by(model.id.desc())
        )
        return query.all()

    @database_logger("tb_user")
    def add_user(
            self,
            **kwargs
    ) -> None:
        if not self._exist(TbUser, dt_userid=kwargs['dt_userid']):
            self._add(TbUser, **kwargs)
            log_info("新用户添加成功", user_id=kwargs.get('dt_userid'), user_name=kwargs.get('dt_name'))
        else:
            self.update_user(
                dt_userid=kwargs['dt_userid'],
                is_admin=kwargs['is_admin'],
                is_active=kwargs['is_active']
            )
            log_info("用户信息更新成功", user_id=kwargs.get('dt_userid'))

    @database_logger("tb_message")
    def add_message(
            self,
            **kwargs
    ) -> None:
        self._add(TbMessage, **kwargs)
        log_info("消息记录成功",
                role=kwargs.get('role'),
                user_id=kwargs.get('dt_userid'),
                content_length=len(kwargs.get('content', '')),
                type=kwargs.get('type'))

    def add_role(
            self,
            **kwargs
    ) -> None:
        self._add(TbRole, **kwargs)

    def add_config(
            self,
            **kwargs
    ) -> None:
        if not self._exist(TbConfig, config_name=kwargs['config_name']):
            self._add(TbConfig, **kwargs)
            log_info("配置添加成功", config_name=kwargs['config_name'])
        else:
            import streamlit as st
            import time
            log_warning("配置已存在", config_name=kwargs['config_name'])
            st.warning('该配置已存在, 窗口将在2秒后关闭！')
            time.sleep(2)

    def add_knowledge(
            self,
            **kwargs
    ) -> None:
        if not self._exist(TbKnowledge, document_name=kwargs['document_name'], relevance_ai=kwargs['relevance_ai']):
            self._add(TbKnowledge, **kwargs)
        else:
            self.update_knowledge(TbKnowledge, **kwargs)

    def query_top(
            self,
            top: int = 10
    ) -> tuple:
        try:
            query = (
                self.session.query(
                    TbMessage.content,
                    func.count(TbMessage.content).label('Count')
                )
                .filter(TbMessage.role == 'USER', TbMessage.create_time >= start_date)
                .group_by(TbMessage.content)
                .order_by(func.count(TbMessage.content).desc())
                .limit(top)
            )
            return query.all()
        except Exception as e:
            raise e
        finally:
            self.session.close()

    @property
    def get_user(self):
        return self._all(TbUser)

    @property
    def get_role(self):
        return self._all(TbRole)

    @property
    def get_auth(self):
        return self._all(TbAuth)

    @property
    def get_messages(self):
        return self._all(TbMessage)

    @property
    def get_config(self):
        return self._all(TbConfig)

    @property
    def get_knowledge(self):
        return self._all(TbKnowledge)

    def get_user_info(
            self,
            **kwargs
    ) -> dict:
        query = (
            self.session.query(
                TbUser.dt_name,
                TbUser.dt_userid,
                TbUser.roles_id,
                TbUser.is_super,
                TbUser.is_admin
            )
            .filter(TbUser.dt_userid == kwargs['dt_userid'])
        ).first()
        keys = ['dt_name', 'dt_userid', 'roles_id', 'is_super', 'is_admin']
        return dict(zip(keys, query))

    @property
    def get_role_ids(
            self
    ) -> list:
        query = (
            self.session.query(
                TbRole.id
            )
        ).all()
        return [role_id[0] for role_id in query]

    def get_user_auth(
            self,
            role_id: int
    ) -> list:
        query = (
            self.session.query(
                TbRole.auth_id
            )
            .filter(TbRole.id == role_id)
        ).first()
        return query[0].split(',')

    def getConfigValue(
            self,
            config_name: str
    ) -> str:
        query = (
            self.session.query(
                TbConfig.config_value
            )
            .filter(TbConfig.config_name == config_name)
        ).first()
        return query[0]
        
    def getConfigValues(
            self,
            config_names: list[str]
    ) -> dict[str, str]:
        """批量获取配置值"""
        query = (
            self.session.query(
                TbConfig.config_name,
                TbConfig.config_value
            )
            .filter(TbConfig.config_name.in_(config_names))
        ).all()
        return {row[0]: row[1] for row in query}

    @property
    def message_type_count(
            self
    ) -> tuple:
        query = (
            self.session.query(
                func.date(TbMessage.create_time).label('date'),
                TbMessage.type,
                func.count(TbMessage.id).label('count')
            )
            .filter(
                and_(TbMessage.role == 'USER', TbMessage.create_time >= start_date)
            )
            .group_by(
                func.date(TbMessage.create_time),
                TbMessage.type
            )
        )
        return query.all()

    @property
    def message_count(
            self
    ) -> tuple:
        query = (
            self.session.query(
                func.date(TbMessage.create_time).label('date'),
                func.count(TbMessage.id).label('total_count')
            )
            .filter(
                and_(TbMessage.role == 'USER', TbMessage.create_time >= start_date)
            )
            .group_by(
                func.date(TbMessage.create_time),
            )
            .order_by(
                func.date(TbMessage.create_time)
            )
        )
        return query.all()
