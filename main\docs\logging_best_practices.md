# 日志系统优化最佳实践

## 概述

本文档描述了系统中日志记录的最佳实践和优化方案。我们提供了统一的日志记录工具和装饰器，以提高日志的一致性、可读性和可维护性。

## 日志架构

### 日志分类

系统将日志分为以下几类：

1. **应用日志** (`app.log`) - 一般应用程序日志
2. **错误日志** (`error.log`) - 错误和异常信息
3. **性能日志** (`performance.log`) - 性能监控和慢操作记录
4. **业务日志** (`business.log`) - 业务事件和用户操作记录

### 日志格式

- **默认格式**: `时间 - 模块名 - 级别 - [文件:行号] - 函数名 - 消息`
- **性能格式**: `时间 - PERF - 级别 - 消息`
- **业务格式**: `时间 - BIZ - 级别 - 消息`
- **错误格式**: `时间 - ERROR - 模块名 - [文件:行号] - 函数名 - 消息`

## 使用方法

### 1. 基础日志记录

```python
from common.log_optimizer import log_info, log_error, log_warning, log_debug

# 记录信息日志 - 支持结构化参数
log_info("用户登录成功", user_id="12345", user_name="张三")
# 输出: 用户登录成功 | user_id=12345 | user_name=张三

# 记录错误日志
try:
    # 一些操作
    pass
except Exception as e:
    log_error("操作失败", error=e, user_id="12345")

# 记录警告日志
log_warning("配置文件缺失", config_file="config.yaml")

# 记录调试日志
log_debug("调试信息", variable_value=some_value)
```

**重要说明**: 我们的日志系统会自动将关键字参数格式化为 `key=value` 的形式，并用 ` | ` 分隔，这样既保持了结构化的优势，又确保了与Python标准logging模块的兼容性。

### 2. 装饰器使用

#### 性能监控装饰器

```python
from common.log_optimizer import performance_logger

@performance_logger(threshold=2.0)  # 超过2秒记录为慢操作
def slow_operation():
    # 耗时操作
    pass
```

#### 业务事件装饰器

```python
from common.log_optimizer import business_logger

@business_logger("用户注册")
def register_user(user_data):
    # 用户注册逻辑
    pass
```

#### 数据库操作装饰器

```python
from common.log_optimizer import database_logger

@database_logger("tb_user")
def add_user(user_info):
    # 数据库操作
    pass
```

### 3. 上下文管理器

```python
from common.log_optimizer import LogOptimizer

optimizer = LogOptimizer()

with optimizer.log_execution_time("复杂业务操作", threshold=5.0):
    # 复杂的业务逻辑
    pass
```

### 4. 专用日志方法

```python
from common.log_optimizer import LogOptimizer

optimizer = LogOptimizer()

# 记录缓存操作
optimizer.log_cache_operation("GET", "user:12345", hit=True, size=1024)

# 记录数据库操作
optimizer.log_database_operation("INSERT", "tb_user", rows_affected=1, duration=0.05)

# 记录业务事件
optimizer.log_business_event("用户下单", user_id="12345", details={"order_id": "ORD001"})

# 记录带上下文的错误
optimizer.log_error_with_context(
    exception, 
    "支付处理失败", 
    {"order_id": "ORD001", "amount": 100.0}
)
```

## 最佳实践

### 1. 日志级别使用

- **DEBUG**: 详细的调试信息，仅在开发环境使用
- **INFO**: 一般信息，记录正常的业务流程
- **WARNING**: 警告信息，不影响程序运行但需要注意
- **ERROR**: 错误信息，程序出现异常但可以继续运行
- **CRITICAL**: 严重错误，程序无法继续运行

### 2. 日志内容规范

#### 好的日志示例

```python
# ✅ 包含关键信息
log_info("用户登录成功", user_id="12345", user_name="张三", login_time="2025-08-01 10:30:00")

# ✅ 错误日志包含上下文
log_error("数据库连接失败", error=e, host="localhost", port=3306, database="app_db")

# ✅ 性能日志包含耗时
log_performance("用户查询操作", duration=1.5, user_count=1000)
```

#### 避免的日志示例

```python
# ❌ 信息不足
log_info("操作成功")

# ❌ 敏感信息泄露
log_info("用户登录", password="123456")

# ❌ 过于冗长
log_info("用户执行了一个非常复杂的操作，包含了很多步骤..." + very_long_string)
```

### 3. 性能考虑

- 使用装饰器自动记录函数执行时间
- 设置合理的慢操作阈值
- 避免在循环中记录大量日志
- 使用异步日志记录（如果需要）

### 4. 安全考虑

- 不要记录敏感信息（密码、令牌等）
- 对用户输入进行适当的清理
- 限制日志文件大小和数量

## 配置说明

### 日志配置参数

```python
# 在 config.yaml 中配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"
  handlers:
    console:
      enabled: true
      level: "INFO"
    file:
      enabled: true
      level: "DEBUG"
      path: "logs/app.log"
      max_bytes: 10485760  # 10MB
      backup_count: 5
```

### 环境变量

- `LOG_LEVEL`: 设置日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `LOG_PATH`: 设置日志文件路径

## 监控和维护

### 日志文件管理

- 日志文件自动轮转，默认10MB一个文件
- 保留最近30个备份文件
- 定期清理过期日志文件

### 日志分析

建议使用以下工具进行日志分析：

1. **ELK Stack** (Elasticsearch, Logstash, Kibana)
2. **Grafana** + **Loki**
3. **Fluentd** + **Prometheus**

### 告警设置

建议为以下情况设置告警：

- 错误日志数量异常增加
- 慢操作频繁出现
- 磁盘空间不足
- 日志文件无法写入

## 迁移指南

### 从旧的日志系统迁移

1. 替换直接的 `logger` 调用：
   ```python
   # 旧方式
   logger.info(f"用户登录: {user_id}")
   
   # 新方式
   log_info("用户登录成功", user_id=user_id)
   ```

2. 添加装饰器到关键函数：
   ```python
   # 为性能敏感的函数添加监控
   @performance_logger(threshold=1.0)
   def critical_function():
       pass
   ```

3. 替换 `print` 语句：
   ```python
   # 旧方式
   print(f"调试信息: {debug_info}")
   
   # 新方式
   log_debug("调试信息", debug_info=debug_info)
   ```

### 逐步迁移策略

1. **第一阶段**: 引入新的日志工具，与旧系统并存
2. **第二阶段**: 逐个模块迁移到新的日志系统
3. **第三阶段**: 移除旧的日志代码，统一使用新系统

## 常见问题

### Q: 如何在不同环境中使用不同的日志级别？

A: 通过环境变量 `LOG_LEVEL` 设置，或在配置文件中为不同环境设置不同的值。

### Q: 如何避免日志文件过大？

A: 系统已配置自动轮转，可以调整 `max_bytes` 和 `backup_count` 参数。

### Q: 如何在生产环境中禁用调试日志？

A: 设置 `LOG_LEVEL=INFO` 或更高级别。

### Q: 如何记录结构化日志？

A: 使用我们提供的便捷函数，传入关键字参数：
```python
log_info("操作完成", user_id=123, operation="update", duration=0.5)
```
