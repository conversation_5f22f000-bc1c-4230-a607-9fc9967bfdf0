#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: log_optimizer.py 
@time: 2025/8/1 
@Description: 日志优化工具类，提供统一的日志记录方法和装饰器
"""
import time
import functools
from typing import Any, Callable, Optional, Dict
from contextlib import contextmanager

from common.myLog import get_logger

class LogOptimizer:
    """日志优化器，提供统一的日志记录方法"""
    
    def __init__(self, logger_name: Optional[str] = None):
        self.logger = get_logger(logger_name)
    
    def log_function_call(self, func_name: str, args: tuple = None, kwargs: dict = None,
                         level: str = "INFO") -> None:
        """记录函数调用日志"""
        args_str = f"args={args}" if args else ""
        kwargs_str = f"kwargs={kwargs}" if kwargs else ""
        params = ", ".join(filter(None, [args_str, kwargs_str]))

        message = f"调用函数: {func_name}"
        if params:
            message += f" | 参数: {params}"

        getattr(self.logger, level.lower())(message)
    
    def log_performance(self, operation: str, duration: float, 
                       threshold: float = 1.0, level: str = "INFO") -> None:
        """记录性能日志"""
        message = f"性能监控: {operation} | 耗时: {duration:.3f}s"
        
        if duration > threshold:
            self.logger.warning(f"慢操作检测: {message} | 超过阈值: {threshold}s")
        else:
            getattr(self.logger, level.lower())(message)
    
    def log_error_with_context(self, error: Exception, context: str, 
                              additional_info: Dict[str, Any] = None) -> None:
        """记录带上下文的错误日志"""
        message = f"错误发生: {context} | 错误类型: {type(error).__name__} | 错误信息: {str(error)}"
        
        if additional_info:
            message += f" | 附加信息: {additional_info}"
            
        self.logger.error(message, exc_info=True)
    
    def log_business_event(self, event: str, user_id: str = None,
                          details: Dict[str, Any] = None, level: str = "INFO") -> None:
        """记录业务事件日志"""
        message = f"业务事件: {event}"

        if user_id:
            message += f" | 用户ID: {user_id}"

        if details:
            details_str = " | ".join([f"{k}={v}" for k, v in details.items()])
            message += f" | {details_str}"

        getattr(self.logger, level.lower())(message)
    
    def log_cache_operation(self, operation: str, key: str, hit: bool = None, 
                           size: int = None) -> None:
        """记录缓存操作日志"""
        message = f"缓存操作: {operation} | 键: {key}"
        
        if hit is not None:
            message += f" | 命中: {'是' if hit else '否'}"
        
        if size is not None:
            message += f" | 大小: {size}"
            
        self.logger.debug(message)
    
    def log_database_operation(self, operation: str, table: str = None, 
                              rows_affected: int = None, duration: float = None) -> None:
        """记录数据库操作日志"""
        message = f"数据库操作: {operation}"
        
        if table:
            message += f" | 表: {table}"
        
        if rows_affected is not None:
            message += f" | 影响行数: {rows_affected}"
        
        if duration is not None:
            message += f" | 耗时: {duration:.3f}s"
            
        self.logger.info(message)
    
    @contextmanager
    def log_execution_time(self, operation: str, threshold: float = 1.0):
        """上下文管理器，记录执行时间"""
        start_time = time.time()
        try:
            self.logger.info(f"开始执行: {operation}")
            yield
        except Exception as e:
            duration = time.time() - start_time
            self.log_error_with_context(
                e, 
                f"执行失败: {operation}",
                {"duration": f"{duration:.3f}s"}
            )
            raise
        else:
            duration = time.time() - start_time
            self.log_performance(operation, duration, threshold)


def performance_logger(threshold: float = 1.0, logger_name: Optional[str] = None):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        optimizer = LogOptimizer(logger_name)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                # 简化参数记录，避免过长的日志
                args_summary = f"({len(args)} args)" if args else ""
                kwargs_summary = f"({len(kwargs)} kwargs)" if kwargs else ""
                optimizer.logger.debug(f"调用函数: {func_name} {args_summary} {kwargs_summary}")

                result = func(*args, **kwargs)

                duration = time.time() - start_time
                optimizer.log_performance(func_name, duration, threshold)

                return result
            except Exception as e:
                duration = time.time() - start_time
                optimizer.log_error_with_context(
                    e,
                    f"函数执行失败: {func_name}",
                    {"duration": f"{duration:.3f}s"}
                )
                raise
        
        return wrapper
    return decorator


def business_logger(event_name: Optional[str] = None, logger_name: Optional[str] = None):
    """业务事件记录装饰器"""
    def decorator(func: Callable) -> Callable:
        optimizer = LogOptimizer(logger_name)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__name__}"
            event = event_name or func_name
            
            try:
                optimizer.logger.info(f"业务事件开始: {event}")
                result = func(*args, **kwargs)
                optimizer.logger.info(f"业务事件完成: {event}")
                return result
            except Exception as e:
                optimizer.logger.error(f"业务事件失败: {event} | 错误: {str(e)}")
                raise
        
        return wrapper
    return decorator


def database_logger(table_name: Optional[str] = None, logger_name: Optional[str] = None):
    """数据库操作记录装饰器"""
    def decorator(func: Callable) -> Callable:
        optimizer = LogOptimizer(logger_name)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 尝试从结果中获取影响行数
                rows_affected = None
                if hasattr(result, 'rowcount'):
                    rows_affected = result.rowcount
                elif isinstance(result, (list, tuple)):
                    rows_affected = len(result)
                
                # 记录数据库操作
                message = f"数据库操作: {func_name}"
                if table_name:
                    message += f" | 表: {table_name}"
                if rows_affected is not None:
                    message += f" | 影响行数: {rows_affected}"
                message += f" | 耗时: {duration:.3f}s"
                optimizer.logger.info(message)
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                message = f"数据库操作失败: {func_name}"
                if table_name:
                    message += f" | 表: {table_name}"
                message += f" | 耗时: {duration:.3f}s | 错误: {str(e)}"
                optimizer.logger.error(message, exc_info=True)
                raise
        
        return wrapper
    return decorator


# 全局日志优化器实例
global_log_optimizer = LogOptimizer()

# 便捷函数
def log_info(message: str, **kwargs):
    """记录信息日志"""
    if kwargs:
        # 将关键字参数格式化为字符串
        params = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        formatted_message = f"{message} | {params}"
    else:
        formatted_message = message
    global_log_optimizer.logger.info(formatted_message)

def log_error(message: str, error: Exception = None, **kwargs):
    """记录错误日志"""
    if error:
        global_log_optimizer.log_error_with_context(error, message, kwargs)
    else:
        if kwargs:
            params = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
            formatted_message = f"{message} | {params}"
        else:
            formatted_message = message
        global_log_optimizer.logger.error(formatted_message)

def log_warning(message: str, **kwargs):
    """记录警告日志"""
    if kwargs:
        params = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        formatted_message = f"{message} | {params}"
    else:
        formatted_message = message
    global_log_optimizer.logger.warning(formatted_message)

def log_debug(message: str, **kwargs):
    """记录调试日志"""
    if kwargs:
        params = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        formatted_message = f"{message} | {params}"
    else:
        formatted_message = message
    global_log_optimizer.logger.debug(formatted_message)

def log_performance(operation: str, duration: float, threshold: float = 1.0):
    """记录性能日志"""
    global_log_optimizer.log_performance(operation, duration, threshold)

def log_business_event(event: str, user_id: str = None, details: Dict[str, Any] = None):
    """记录业务事件"""
    message = f"业务事件: {event}"
    if user_id:
        message += f" | 用户ID: {user_id}"
    if details:
        details_str = " | ".join([f"{k}={v}" for k, v in details.items()])
        message += f" | {details_str}"
    global_log_optimizer.logger.info(message)
